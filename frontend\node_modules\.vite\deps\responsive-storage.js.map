{"version": 3, "sources": ["../../.pnpm/responsive-storage@2.2.0/node_modules/responsive-storage/dist/index.mjs"], "sourcesContent": ["\"use strict\";var p=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var u=Object.getOwnPropertyNames;var j=Object.prototype.hasOwnProperty;var _=(o,t,e)=>t in o?p(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var P=(o,t)=>{for(var e in t)p(o,e,{get:t[e],enumerable:!0})},y=(o,t,e,s)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let r of u(t))!j.call(o,r)&&r!==e&&p(o,r,{get:()=>t[r],enumerable:!(s=d(t,r))||s.enumerable});return o},m=(o,t,e)=>(y(o,t,\"default\"),e&&y(e,t,\"default\"));var f=(o,t,e)=>(_(o,typeof t!=\"symbol\"?t+\"\":t,e),e);var c={};P(c,{Vue:()=>I});m(c,x);import*as I from\"vue\";import*as x from\"vue\";var g=class{static install(t,e){let{nameSpace:s=this._nameSpace,memory:r}=e;return r&&this.clearAll(s,r),new g(t,e)}static clearAll(t,e){Object.keys(e).forEach(s=>{let r=t+s;Object.prototype.hasOwnProperty.call(window.localStorage,r)&&window.localStorage.removeItem(r)})}static get(t){return JSON.parse(window.localStorage.getItem(t))}static set(t,e){e=typeof e==\"object\"?JSON.stringify(e):e,window.localStorage.setItem(t,e)}static getData(t,e){if(Object.prototype.hasOwnProperty.call(window.localStorage,this._getStaticKey(e,t)))return JSON.parse(window.localStorage.getItem(this._getStaticKey(e,t)))}constructor(t,e){let s=g,{version:r=3,nameSpace:S=s._nameSpace,memory:w}=e,l=a=>S+a,i=r===3?(0,c.reactive)(w):w;Object.keys(i).length===0&&console.warn(\"key cannot be empty\"),Object.keys(i).forEach(a=>{let b=i[a];s.set(l(a),b),Reflect.defineProperty(i,a,{get:()=>s.get(l(a)),set:h=>s.set(l(a),h),configurable:!0}),r===2&&t.util.defineReactive(i,a,i[a])});let O=r===3?t.config.globalProperties:t.prototype;Reflect.defineProperty(O,\"$storage\",{get:()=>i})}},n=g;f(n,\"_nameSpace\",\"rs-\"),f(n,\"_getStaticKey\",(t,e)=>`${t??g._nameSpace}${e}`);export{n as default};\n"], "mappings": ";;;;;;AAAa,IAAI,IAAE,OAAO;AAAe,IAAI,IAAE,OAAO;AAAyB,IAAI,IAAE,OAAO;AAAoB,IAAI,IAAE,OAAO,UAAU;AAAe,IAAI,IAAE,CAAC,GAAE,GAAE,MAAI,KAAK,IAAE,EAAE,GAAE,GAAE,EAAC,YAAW,MAAG,cAAa,MAAG,UAAS,MAAG,OAAM,EAAC,CAAC,IAAE,EAAE,CAAC,IAAE;AAAE,IAAI,IAAE,CAAC,GAAE,MAAI;AAAC,WAAQ,KAAK,EAAE,GAAE,GAAE,GAAE,EAAC,KAAI,EAAE,CAAC,GAAE,YAAW,KAAE,CAAC;AAAC;AAA5D,IAA8D,IAAE,CAAC,GAAE,GAAE,GAAE,MAAI;AAAC,MAAG,KAAG,OAAO,KAAG,YAAU,OAAO,KAAG,WAAW,UAAQ,KAAK,EAAE,CAAC,EAAE,EAAC,EAAE,KAAK,GAAE,CAAC,KAAG,MAAI,KAAG,EAAE,GAAE,GAAE,EAAC,KAAI,MAAI,EAAE,CAAC,GAAE,YAAW,EAAE,IAAE,EAAE,GAAE,CAAC,MAAI,EAAE,WAAU,CAAC;AAAE,SAAO;AAAC;AAArO,IAAuO,IAAE,CAAC,GAAE,GAAE,OAAK,EAAE,GAAE,GAAE,SAAS,GAAE,KAAG,EAAE,GAAE,GAAE,SAAS;AAAG,IAAI,IAAE,CAAC,GAAE,GAAE,OAAK,EAAE,GAAE,OAAO,KAAG,WAAS,IAAE,KAAG,GAAE,CAAC,GAAE;AAAG,IAAI,IAAE,CAAC;AAAE,EAAE,GAAE,EAAC,KAAI,MAAI,gCAAC,CAAC;AAAE,EAAE,GAAE,+BAAC;AAA8C,IAAI,IAAE,MAAK;AAAA,EAAC,OAAO,QAAQ,GAAE,GAAE;AAAC,QAAG,EAAC,WAAU,IAAE,KAAK,YAAW,QAAO,EAAC,IAAE;AAAE,WAAO,KAAG,KAAK,SAAS,GAAE,CAAC,GAAE,IAAI,EAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,SAAS,GAAE,GAAE;AAAC,WAAO,KAAK,CAAC,EAAE,QAAQ,OAAG;AAAC,UAAI,IAAE,IAAE;AAAE,aAAO,UAAU,eAAe,KAAK,OAAO,cAAa,CAAC,KAAG,OAAO,aAAa,WAAW,CAAC;AAAA,IAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,IAAI,GAAE;AAAC,WAAO,KAAK,MAAM,OAAO,aAAa,QAAQ,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,IAAI,GAAE,GAAE;AAAC,QAAE,OAAO,KAAG,WAAS,KAAK,UAAU,CAAC,IAAE,GAAE,OAAO,aAAa,QAAQ,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,QAAQ,GAAE,GAAE;AAAC,QAAG,OAAO,UAAU,eAAe,KAAK,OAAO,cAAa,KAAK,cAAc,GAAE,CAAC,CAAC,EAAE,QAAO,KAAK,MAAM,OAAO,aAAa,QAAQ,KAAK,cAAc,GAAE,CAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAY,GAAE,GAAE;AAAC,QAAI,IAAE,GAAE,EAAC,SAAQ,IAAE,GAAE,WAAU,IAAE,EAAE,YAAW,QAAO,EAAC,IAAE,GAAE,IAAE,OAAG,IAAE,GAAE,IAAE,MAAI,KAAG,GAAE,EAAE,UAAU,CAAC,IAAE;AAAE,WAAO,KAAK,CAAC,EAAE,WAAS,KAAG,QAAQ,KAAK,qBAAqB,GAAE,OAAO,KAAK,CAAC,EAAE,QAAQ,OAAG;AAAC,UAAI,IAAE,EAAE,CAAC;AAAE,QAAE,IAAI,EAAE,CAAC,GAAE,CAAC,GAAE,QAAQ,eAAe,GAAE,GAAE,EAAC,KAAI,MAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAE,KAAI,OAAG,EAAE,IAAI,EAAE,CAAC,GAAE,CAAC,GAAE,cAAa,KAAE,CAAC,GAAE,MAAI,KAAG,EAAE,KAAK,eAAe,GAAE,GAAE,EAAE,CAAC,CAAC;AAAA,IAAC,CAAC;AAAE,QAAI,IAAE,MAAI,IAAE,EAAE,OAAO,mBAAiB,EAAE;AAAU,YAAQ,eAAe,GAAE,YAAW,EAAC,KAAI,MAAI,EAAC,CAAC;AAAA,EAAC;AAAC;AAAhiC,IAAkiC,IAAE;AAAE,EAAE,GAAE,cAAa,KAAK,GAAE,EAAE,GAAE,iBAAgB,CAAC,GAAE,MAAI,GAAG,KAAG,EAAE,UAAU,GAAG,CAAC,EAAE;", "names": []}