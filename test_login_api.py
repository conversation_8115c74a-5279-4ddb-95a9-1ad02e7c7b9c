#!/usr/bin/env python3
"""
测试登录API的简单脚本
运行前确保Django服务器在8000端口运行
"""

import requests
import json

def test_login_api():
    login_url = "http://localhost:8000/user/login"
    info_url = "http://localhost:8000/user/info"

    # 测试管理员登录
    admin_data = {
        "username": "admin",
        "password": "admin@123"
    }

    print("测试管理员登录...")
    try:
        response = requests.post(login_url, json=admin_data, headers={'Content-Type': 'application/json'})
        print(f"状态码: {response.status_code}")
        login_result = response.json()
        print(f"登录响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")

        # 如果登录成功，测试获取用户信息
        if login_result.get('success') and 'token' in login_result.get('data', {}):
            token = login_result['data']['token']
            print(f"\n获取用户信息...")
            info_response = requests.get(info_url, headers={
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            })
            print(f"状态码: {info_response.status_code}")
            print(f"用户信息响应: {json.dumps(info_response.json(), indent=2, ensure_ascii=False)}")

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保Django服务器在8000端口运行")
        return
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return
    
    print("\n" + "="*50 + "\n")
    
    # 测试普通用户登录
    common_data = {
        "username": "common",
        "password": "common"
    }
    
    print("测试普通用户登录...")
    try:
        response = requests.post(login_url, json=common_data, headers={'Content-Type': 'application/json'})
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试错误凭据
    wrong_data = {
        "username": "admin",
        "password": "wrong"
    }
    
    print("测试错误凭据...")
    try:
        response = requests.post(login_url, json=wrong_data, headers={'Content-Type': 'application/json'})
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试登录API...")
    print("确保Django服务器正在运行: python manage.py runserver 8000")
    print("="*50)
    test_login_api()
    print("✅ 测试完成")
