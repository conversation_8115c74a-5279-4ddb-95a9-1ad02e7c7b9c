import { http } from "@/utils/http";

export type LoginResult = {
  success: boolean;
  message: string;
  data: {
    /** JWT token */
    token: string;
  };
};

export type UserInfoResult = {
  success: boolean;
  data: {
    /** 用户ID */
    id: number;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 头像 */
    avatar: string;
    /** 邮箱 */
    email: string | null;
    /** 手机号 */
    phonenumber: string | null;
    /** 账号状态 (0正常 1停用) */
    status: number | null;
    /** 最后登录时间 */
    login_date: string | null;
    /** 创建时间 */
    create_time: string | null;
    /** 更新时间 */
    update_time: string | null;
    /** 备注 */
    remark: string | null;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<LoginResult>("post", "/api/user/login", { data });
};

/** 获取用户信息 */
export const getUserInfo = () => {
  return http.request<UserInfoResult>("get", "/api/user/info");
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};
