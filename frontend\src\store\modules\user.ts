import { defineStore } from "pinia";
import {
  type userType,
  store,
  router,
  resetRouter,
  routerArrays,
  storageLocal
} from "../utils";
import {
  type LoginResult,
  type UserInfoResult,
  type RefreshTokenResult,
  getLogin,
  getUserInfo,
  refreshTokenApi
} from "@/api/user";
import { useMultiTagsStoreHook } from "./multiTags";
import { type DataInfo, setToken, removeToken, setUserInfo, userKey } from "@/utils/auth";

export const useUserStore = defineStore("pure-user", {
  state: (): userType => ({
    // 头像
    avatar: storageLocal().getItem<DataInfo>(userKey)?.avatar ?? "",
    // 用户名
    username: storageLocal().getItem<DataInfo>(userKey)?.username ?? "",
    // 昵称
    nickname: storageLocal().getItem<DataInfo>(userKey)?.nickname ?? "",
    // 页面级别权限
    roles: storageLocal().getItem<DataInfo>(userKey)?.roles ?? [],
    // 按钮级别权限
    permissions:
      storageLocal().getItem<DataInfo>(userKey)?.permissions ?? [],
    // 是否勾选了登录页的免登录
    isRemembered: false,
    // 登录页的免登录存储几天，默认7天
    loginDay: 7
  }),
  actions: {
    /** 存储头像 */
    SET_AVATAR(avatar: string) {
      this.avatar = avatar;
    },
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储昵称 */
    SET_NICKNAME(nickname: string) {
      this.nickname = nickname;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储按钮级别权限 */
    SET_PERMS(permissions: Array<string>) {
      this.permissions = permissions;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 设置登录页的免登录存储几天 */
    SET_LOGINDAY(value: number) {
      this.loginDay = Number(value);
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<LoginResult>((resolve, reject) => {
        getLogin(data)
          .then(async loginData => {
            if (loginData?.success) {
              // 设置token
              setToken(loginData.data);

              try {
                // 获取用户信息
                const userInfo = await getUserInfo();
                if (userInfo?.success) {
                  // 设置用户信息到store和localStorage
                  this.SET_AVATAR(userInfo.data.avatar);
                  this.SET_USERNAME(userInfo.data.username);
                  this.SET_NICKNAME(userInfo.data.nickname);
                  this.SET_ROLES(userInfo.data.roles);
                  this.SET_PERMS(userInfo.data.permissions);

                  // 保存到localStorage
                  setUserInfo({
                    id: userInfo.data.id,
                    username: userInfo.data.username,
                    nickname: userInfo.data.nickname,
                    avatar: userInfo.data.avatar,
                    email: userInfo.data.email,
                    phonenumber: userInfo.data.phonenumber,
                    status: userInfo.data.status,
                    login_date: userInfo.data.login_date,
                    create_time: userInfo.data.create_time,
                    update_time: userInfo.data.update_time,
                    remark: userInfo.data.remark,
                    roles: userInfo.data.roles,
                    permissions: userInfo.data.permissions
                  });
                }
              } catch (error) {
                console.warn('获取用户信息失败:', error);
              }
            }
            resolve(loginData);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      this.username = "";
      this.roles = [];
      this.permissions = [];
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },
    /** 刷新`token` */
    async handRefreshToken(data) {
      return new Promise<RefreshTokenResult>((resolve, reject) => {
        refreshTokenApi(data)
          .then(data => {
            if (data) {
              setToken(data.data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
