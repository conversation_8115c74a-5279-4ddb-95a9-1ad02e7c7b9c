# Generated manually to update datetime fields

from django.db import migrations, models
from django.utils import timezone


def convert_date_to_datetime(apps, schema_editor):
    """将现有的日期数据转换为日期时间格式"""
    SysUser = apps.get_model('user', 'SysUser')
    
    # 更新现有记录，将日期转换为日期时间
    for user in SysUser.objects.all():
        if user.create_time:
            # 如果create_time是日期，转换为当天的开始时间
            if hasattr(user.create_time, 'date'):
                # 已经是datetime，不需要转换
                pass
            else:
                # 是date，需要转换为datetime
                user.create_time = timezone.datetime.combine(
                    user.create_time, 
                    timezone.datetime.min.time()
                ).replace(tzinfo=timezone.get_current_timezone())
        
        if user.update_time:
            if hasattr(user.update_time, 'date'):
                pass
            else:
                user.update_time = timezone.datetime.combine(
                    user.update_time, 
                    timezone.datetime.min.time()
                ).replace(tzinfo=timezone.get_current_timezone())
        
        if user.login_date:
            if hasattr(user.login_date, 'date'):
                pass
            else:
                user.login_date = timezone.datetime.combine(
                    user.login_date, 
                    timezone.datetime.min.time()
                ).replace(tzinfo=timezone.get_current_timezone())
        
        user.save()


def reverse_datetime_to_date(apps, schema_editor):
    """回滚操作：将日期时间转换回日期"""
    SysUser = apps.get_model('user', 'SysUser')
    
    for user in SysUser.objects.all():
        if user.create_time:
            user.create_time = user.create_time.date()
        if user.update_time:
            user.update_time = user.update_time.date()
        if user.login_date:
            user.login_date = user.login_date.date()
        user.save()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_add_sample_users'),
    ]

    operations = [
        # 首先运行数据转换
        migrations.RunPython(convert_date_to_datetime, reverse_datetime_to_date),
        
        # 然后修改字段类型
        migrations.AlterField(
            model_name='sysuser',
            name='create_time',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='sysuser',
            name='update_time',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='sysuser',
            name='login_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间'),
        ),
        migrations.AlterField(
            model_name='sysuser',
            name='status',
            field=models.IntegerField(default=0, verbose_name='帐号状态（0正常 1停用）'),
        ),
        migrations.AlterField(
            model_name='sysuser',
            name='remark',
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name='备注'),
        ),
        
        # 添加模型元数据
        migrations.AlterModelOptions(
            name='sysuser',
            options={'ordering': ['-create_time'], 'verbose_name': '系统用户', 'verbose_name_plural': '系统用户'},
        ),
    ]
