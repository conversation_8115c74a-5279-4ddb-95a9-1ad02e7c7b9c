from django.db import models
from django.utils import timezone

# Create your models here.
class SysUser(models.Model):
    id = models.AutoField(primary_key=True)
    username = models.CharField(max_length=100, unique=True, verbose_name="用户名")
    password = models.Char<PERSON>ield(max_length=100, verbose_name="密码")
    nickname = models.CharField(max_length=100, null=True, verbose_name="昵称")
    avatar = models.CharField(max_length=255, null=True, verbose_name="用户头像")
    email = models.Char<PERSON>ield(max_length=100, null=True, verbose_name="用户邮箱")
    phonenumber = models.Char<PERSON>ield(max_length=11, null=True, verbose_name="手机号码")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    login_date = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    status = models.IntegerField(default=0, verbose_name="帐号状态（0正常 1停用）")
    remark = models.Char<PERSON><PERSON>(max_length=500, null=True, blank=True, verbose_name="备注")

    class Meta:
        db_table = "sys_user"
        verbose_name = "系统用户"
        verbose_name_plural = "系统用户"
        ordering = ['-create_time']  # 按创建时间倒序排列

    def __str__(self):
        return self.username