import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 密码正则（密码长度不小于8位） */
export const REGEXP_PWD = /^.{8,}$/;

/** 登录校验 */
const loginRules = reactive<FormRules>({
  password: [
    {
      validator: (_rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入密码"));
        } else if (!REGEXP_PWD.test(value)) {
          callback(
            new Error("密码长度不能少于8位")
          );
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

export { loginRules };
