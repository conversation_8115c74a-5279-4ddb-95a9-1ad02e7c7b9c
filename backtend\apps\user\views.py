from django.shortcuts import render
from django.http import JsonResponse
from .models import SysUser
from django.views import View
from rest_framework_jwt.settings import api_settings
import json
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils import timezone

# Create your views here.
# TestView
class TestView(View):
    def get(self,request):
        return JsonResponse({'code': 200, 'info': 'ok'})

class UsersView(View):
    def get(self, _request):
        users = SysUser.objects.all().values()
        return JsonResponse({
            'success': True,
            'data': list(users)
        })

    
class JwtTestView(View):
    def get(self, _request):
        try:
            user = SysUser.objects.get(username='admin', password='admin@123')
            jwt_payload_handler = api_settings.JWT_PAYLOAD_HANDLER
            jwt_encode_handler = api_settings.JWT_ENCODE_HANDLER
            payload = jwt_payload_handler(user)
            token = jwt_encode_handler(payload)
            return JsonResponse({'success': True, 'token': token})
        except SysUser.DoesNotExist:
            return JsonResponse({'success': False, 'message': '用户不存在'})


@method_decorator(csrf_exempt, name='dispatch')
class LoginView(View):
    def post(self, request):
        try:
            data = json.loads(request.body)
            username = data.get('username')
            password = data.get('password')

            # 参数验证
            if not username or not password:
                return JsonResponse({
                    'success': False,
                    'message': '用户名和密码不能为空！'
                })

            # 用户验证
            try:
                user = SysUser.objects.get(username=username, password=password)
            except SysUser.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '用户名或密码错误'
                })

            # 状态检查
            if user.status == 1:
                return JsonResponse({
                    'success': False,
                    'message': '账户已被停用'
                })

            # 生成token
            jwt_payload_handler = api_settings.JWT_PAYLOAD_HANDLER
            jwt_encode_handler = api_settings.JWT_ENCODE_HANDLER
            payload = jwt_payload_handler(user)
            token = jwt_encode_handler(payload)

            # 更新登录时间
            user.login_date = timezone.now()
            user.save()

            return JsonResponse({
                'success': True,
                'message': '登录成功',
                'data': {'token': token}
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'message': '数据为空'
            })
        except Exception:
            return JsonResponse({
                'success': False,
                'message': '服务器内部错误'
            })


@method_decorator(csrf_exempt, name='dispatch')
class UserInfoView(View):
    def get(self, request):
        """获取当前用户信息"""
        try:
            # 获取token
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if not auth_header or not auth_header.startswith('Bearer '):
                return JsonResponse({
                    'success': False,
                    'message': '未提供有效的认证token'
                })

            token = auth_header.split(' ')[1]

            # 解码token获取用户
            try:
                from rest_framework_jwt.utils import jwt_decode_handler
                payload = jwt_decode_handler(token)
                user_id = payload.get('user_id')

                if not user_id:
                    return JsonResponse({
                        'success': False,
                        'message': '无效的token'
                    })

                user = SysUser.objects.get(id=user_id)

            except (ValueError, KeyError, SysUser.DoesNotExist):
                return JsonResponse({
                    'success': False,
                    'message': '无效的token或用户不存在'
                })

            # 设置角色权限
            if user.username == 'admin':
                roles = ['admin']
                permissions = ['*:*:*']
            else:
                roles = ['common']
                permissions = ['permission:btn:add', 'permission:btn:edit']

            return JsonResponse({
                'success': True,
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'nickname': user.remark or user.username,
                    'avatar': user.avatar,
                    'email': user.email,
                    'phonenumber': user.phonenumber,
                    'status': user.status,
                    'login_date': user.login_date.strftime('%Y-%m-%d %H:%M:%S') if user.login_date else None,
                    'create_time': user.create_time.strftime('%Y-%m-%d %H:%M:%S') if user.create_time else None,
                    'update_time': user.update_time.strftime('%Y-%m-%d %H:%M:%S') if user.update_time else None,
                    'remark': user.remark,
                    'roles': roles,
                    'permissions': permissions
                }
            })

        except Exception:
            return JsonResponse({
                'success': False,
                'message': '服务器内部错误'
            })

