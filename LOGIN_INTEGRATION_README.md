# 前后端登录集成说明

## 修改内容

### 后端修改 (Django)

1. **新增登录视图** (`backtend/apps/user/views.py`)
   - 添加了 `LoginView` 类，处理 POST 请求
   - 验证用户名和密码
   - 生成 JWT token
   - **安全改进**: 只返回token信息，不返回敏感的用户详细信息

2. **新增用户信息视图** (`backtend/apps/user/views.py`)
   - 添加了 `UserInfoView` 类，处理 GET 请求
   - 需要Bearer token认证
   - 返回用户的详细信息（头像、昵称、角色、权限等）

2. **更新URL配置** (`backtend/apps/user/urls.py`)
   - 添加了 `/user/login` 路由
   - 添加了 `/user/info` 路由

3. **更新设置** (`backtend/system/settings.py`)
   - 添加了 `ALLOWED_HOSTS` 配置以允许本地访问
   - 添加了自定义JWT配置和payload处理器
   - 确保JWT token包含正确的用户信息

4. **添加示例用户数据** (`backtend/apps/user/migrations/0003_add_sample_users.py`)
   - 创建了 admin/admin@123 和 common/common@123 两个测试用户

5. **优化时间字段** (`backtend/apps/user/migrations/0004_update_datetime_fields.py`)
   - 将DateField改为DateTimeField，支持精确到秒的时间记录
   - 添加了auto_now_add和auto_now自动时间戳
   - 符合实际开发规范的时间处理

### 前端修改 (Vue.js)

1. **修改登录API调用** (`frontend/src/api/user.ts`)
   - 登录接口改为直接调用后端API (`/api/user/login`)
   - 使用Vite代理配置，`/api` 前缀会被代理到 `http://localhost:8000`

2. **禁用登录mock** (`frontend/mock/login.ts`)
   - 注释掉了mock拦截器，让前端直接调用后端
   - 保留了mock代码供开发测试使用（需要时可取消注释）

## 设置步骤

### 1. 后端设置

```bash
cd backtend

# 安装依赖 (如果还没有安装)
pip install django djangorestframework djangorestframework-jwt django-cors-headers pymysql

# 确保MySQL数据库运行并创建了 'system' 数据库

# 运行迁移
python manage.py migrate

# 启动Django服务器
python manage.py runserver 8000
```

### 2. 前端设置

```bash
cd frontend

# 安装依赖 (如果还没有安装)
npm install
# 或
pnpm install

# 启动开发服务器
npm run dev
# 或
pnpm dev
```

## 测试用户

系统已预置两个测试用户：

1. **管理员用户**
   - 用户名: `admin`
   - 密码: `admin@123`
   - 邮箱: `<EMAIL>`
   - 备注: `系统管理员`
   - 角色: admin
   - 权限: `*:*:*` (所有权限)

2. **普通用户**
   - 用户名: `common`
   - 密码: `common@123`
   - 邮箱: `<EMAIL>`
   - 备注: `普通用户`
   - 角色: common
   - 权限: `permission:btn:add`, `permission:btn:edit`

## API端点

- **登录**: `POST /user/login`
  - 请求体: `{"username": "admin", "password": "admin@123"}`
  - 响应:
    ```json
    {
      "success": true,
      "message": "登录成功",
      "data": {
        "token": "jwt_token_here"
      }
    }
    ```

- **获取用户信息**: `GET /user/info`
  - 请求头: `Authorization: Bearer jwt_token_here`
  - 响应:
    ```json
    {
      "success": true,
      "data": {
        "id": 1,
        "username": "admin",
        "nickname": "系统管理员",
        "avatar": "https://avatars.githubusercontent.com/u/167622633",
        "email": "<EMAIL>",
        "phonenumber": null,
        "status": 0,
        "login_date": "2024-08-11 14:30:25",
        "create_time": "2024-08-11 10:15:30",
        "update_time": "2024-08-11 14:30:25",
        "remark": "系统管理员",
        "roles": ["admin"],
        "permissions": ["*:*:*"]
      }
    }
    ```

## 工作原理

1. 前端发送登录请求到 `/api/user/login`
2. Vite代理将 `/api` 请求转发到 `http://localhost:8000`
3. Django后端验证用户凭据并返回JWT token（**符合RESTful规范，只返回必要信息**）
4. 前端收到token后，自动调用 `/api/user/info` 获取用户详细信息
5. 后端验证token并返回用户信息（头像、昵称、角色、权限等）
6. 前端将用户信息存储到store和localStorage中

## 故障排除

1. **CORS错误**: 确保Django设置中的CORS配置正确
2. **数据库连接错误**: 确保MySQL服务运行且数据库存在
3. **JWT错误**: 确保安装了 `djangorestframework-jwt`
4. **代理错误**: 检查Vite配置中的代理设置
5. **Token验证失败**:
   - 确保JWT配置中的payload处理器正确设置
   - 检查token是否正确传递到Authorization头
   - 验证用户信息接口的token解析逻辑

## 注意事项

- 这是开发环境配置，生产环境需要额外的安全措施
- 密码应该加密存储（当前为明文存储，仅用于演示）
- JWT密钥应该使用环境变量配置
- 建议添加密码复杂度验证和登录失败限制
