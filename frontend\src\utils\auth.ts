import Cookies from "js-cookie";
import { useUserStoreHook } from "@/store/modules/user";
import { storageLocal, isString, isIncludeAllChildren } from "@pureadmin/utils";

export interface DataInfo {
  /** JWT token */
  token?: string;
  /** 用户ID */
  id?: number;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 邮箱 */
  email?: string | null;
  /** 手机号 */
  phonenumber?: string | null;
  /** 账号状态 */
  status?: number | null;
  /** 最后登录时间 */
  login_date?: string | null;
  /** 创建时间 */
  create_time?: string | null;
  /** 更新时间 */
  update_time?: string | null;
  /** 备注 */
  remark?: string | null;
  /** 当前登录用户的角色 */
  roles?: Array<string>;
  /** 当前登录用户的按钮级别权限 */
  permissions?: Array<string>;
}

export const userKey = "user-info";
export const TokenKey = "authorized-token";
/**
 * 通过`multiple-tabs`是否在`cookie`中，判断用户是否已经登录系统，
 * 从而支持多标签页打开已经登录的系统后无需再登录。
 * 浏览器完全关闭后`multiple-tabs`将自动从`cookie`中销毁，
 * 再次打开浏览器需要重新登录系统
 * */
export const multipleTabsKey = "multiple-tabs";

/** 获取`token` */
export function getToken(): string | null {
  // 从cookie中获取token
  const tokenData = Cookies.get(TokenKey);
  if (tokenData) {
    try {
      const parsed = JSON.parse(tokenData);
      return parsed.token;
    } catch {
      return null;
    }
  }

  // 从localStorage获取token
  const userData = storageLocal().getItem<DataInfo>(userKey);
  return userData?.token || null;
}

/**
 * @description 设置JWT token
 */
export function setToken(data: { token: string }) {
  const { isRemembered, loginDay } = useUserStoreHook();
  const tokenData = JSON.stringify({ token: data.token });

  // 设置token到cookie
  Cookies.set(TokenKey, tokenData);

  Cookies.set(
    multipleTabsKey,
    "true",
    isRemembered
      ? {
          expires: loginDay
        }
      : {}
  );


}

/** 设置用户信息到localStorage */
export function setUserInfo(userInfo: Omit<DataInfo, 'token'>) {
  const existingData = storageLocal().getItem<DataInfo>(userKey) || {};
  storageLocal().setItem(userKey, {
    ...existingData,
    ...userInfo
  });
}

/** 删除`token`以及key值为`user-info`的localStorage信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  Cookies.remove(multipleTabsKey);
  storageLocal().removeItem(userKey);
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};

/** 是否有按钮级别的权限（根据登录接口返回的`permissions`字段进行判断）*/
export const hasPerms = (value: string | Array<string>): boolean => {
  if (!value) return false;
  const allPerms = "*:*:*";
  const { permissions } = useUserStoreHook();
  if (!permissions) return false;
  if (permissions.length === 1 && permissions[0] === allPerms) return true;
  const isAuths = isString(value)
    ? permissions.includes(value)
    : isIncludeAllChildren(value, permissions);
  return isAuths ? true : false;
};
