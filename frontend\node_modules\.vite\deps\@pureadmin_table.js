import {
  ElConfigProvider,
  ElPagination,
  ElTable,
  ElTableColumn,
  vLoading
} from "./chunk-7YS2N3KF.js";
import "./chunk-TG4HWPS3.js";
import {
  Fragment,
  computed,
  createVNode,
  defineComponent,
  getCurrentInstance,
  inject,
  isVNode,
  mergeProps,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  resolveDirective,
  toRefs,
  unref,
  vue_runtime_esm_bundler_exports,
  warn,
  withDirectives
} from "./chunk-27GFB46B.js";
import "./chunk-G3PMV62Z.js";

// node_modules/.pnpm/@pureadmin+table@3.3.0_elem_7ac43b79ebc2cbe96b9197a9d1a56185/node_modules/@pureadmin/table/dist/index.es.js
var Me = Object.prototype.hasOwnProperty;
var Z = (e, t) => Me.call(e, t);
var ee = (e) => e !== null && typeof e == "object";
var te = "__epPropKey";
var Ie = ((e, t) => {
  if (!ee(e) || ee(r = e) && r[te])
    return e;
  var r;
  const { values: c, required: u, default: d, type: i, validator: j } = e, x = c || j ? (w) => {
    let f = false, b = [];
    if (c && (b = Array.from(c), Z(e, "default") && b.push(d), f || (f = b.includes(w))), j && (f || (f = j(w))), !f && b.length > 0) {
      const p = [...new Set(b)].map(($) => JSON.stringify($)).join(", ");
      warn(`Invalid prop: validation failed${t ? ` for prop "${t}"` : ""}. Expected one of [${p}], got value ${JSON.stringify(w)}.`);
    }
    return f;
  } : void 0, F = { type: i, required: !!u, validator: x, [te]: true };
  return Z(e, "default") && (F.default = d), F;
})({ type: String, values: ["", "default", "small", "large"], required: false });
var We = { data: { type: Array, default: () => [] }, size: Ie, width: [String, Number], height: [String, Number], maxHeight: [String, Number], fit: { type: Boolean, default: true }, stripe: Boolean, border: Boolean, rowKey: [String, Function], showHeader: { type: Boolean, default: true }, showSummary: Boolean, sumText: String, summaryMethod: Function, rowClassName: [String, Function], rowStyle: [Object, Function], cellClassName: [String, Function], cellStyle: [Object, Function], headerRowClassName: [String, Function], headerRowStyle: [Object, Function], headerCellClassName: [String, Function], headerCellStyle: [Object, Function], highlightCurrentRow: Boolean, currentRowKey: [String, Number], emptyText: String, expandRowKeys: Array, defaultExpandAll: Boolean, defaultSort: Object, tooltipEffect: String, tooltipOptions: Object, spanMethod: Function, selectOnIndeterminate: { type: Boolean, default: true }, indent: { type: Number, default: 16 }, treeProps: { type: Object, default: () => ({ hasChildren: "hasChildren", children: "children", checkStrictly: false }) }, lazy: Boolean, load: Function, style: { type: Object, default: () => ({}) }, className: { type: String, default: "" }, tableLayout: { type: String, default: "fixed" }, scrollbarAlwaysOn: Boolean, flexible: Boolean, showOverflowTooltip: [Boolean, Object], tooltipFormatter: Function, appendFilterPanelTo: String, scrollbarTabindex: { type: [Number, String], default: void 0 }, allowDragLastColumn: { type: Boolean, default: true }, preserveExpandedContent: Boolean };
var Ke = { tableKey: { type: [String, Number], default: "0" }, columns: { type: Array, default: [] }, loading: { type: Boolean, default: false }, loadingConfig: { type: Object, default: () => {
} }, alignWhole: { type: String, default: "left" }, headerAlign: { type: String, default: "" }, showOverflowTooltip: { type: Boolean, default: false }, rowHoverBgColor: { type: String, default: "" }, pagination: { type: Object, default: { total: 0, pageSize: 5, align: "right", size: "default", background: false, pageSizes: [5, 10, 15, 20], layout: "total, sizes, prev, pager, next, jumper" } }, adaptive: { type: Boolean, default: false }, adaptiveConfig: { type: Object, default: { offsetBottom: 96, fixHeader: true, timeout: 60, zIndex: 3 } }, locale: { type: [String, Object], default: "" }, ...We };
var ae = defineComponent({ name: "Renderer", props: { render: { type: Function }, params: { type: Object } }, setup: (e) => () => createVNode(Fragment, null, [e.render(e.params)]) });
var ne = { name: "en", el: { select: { loading: "Loading", noMatch: "No matching data", noData: "No data", placeholder: "Select" }, pagination: { goto: "Go to", pagesize: "/page", total: "Total {total}", pageClassifier: "", page: "Page", prev: "Go to previous page", next: "Go to next page", currentPage: "page {pager}", prevPages: "Previous {pager} pages", nextPages: "Next {pager} pages", deprecationWarning: "Deprecated usages detected, please refer to the el-pagination documentation for more details" }, table: { emptyText: "No Data", confirmFilter: "Confirm", resetFilter: "Reset", clearFilter: "All", sumText: "Sum" } } };
var oe = { name: "zh-cn", el: { select: { loading: "加载中", noMatch: "无匹配数据", noData: "无数据", placeholder: "请选择" }, pagination: { goto: "前往", pagesize: "条/页", total: "共 {total} 条", pageClassifier: "页", page: "页", prev: "上一页", next: "下一页", currentPage: "第 {pager} 页", prevPages: "向前 {pager} 页", nextPages: "向后 {pager} 页", deprecationWarning: "你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档" }, table: { emptyText: "暂无数据", confirmFilter: "筛选", resetFilter: "重置", clearFilter: "全部", sumText: "合计" } } };
var re = { name: "zh-tw", el: { select: { loading: "載入中", noMatch: "無相符資料", noData: "無資料", placeholder: "請選擇" }, pagination: { goto: "前往", pagesize: "項/頁", total: "共 {total} 項", pageClassifier: "頁", page: "頁", prev: "上一頁", next: "下一頁", currentPage: "第 {pager} 頁", prevPages: "向前 {pager} 頁", nextPages: "向后 {pager} 頁", deprecationWarning: "偵測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊" }, table: { emptyText: "暫無資料", confirmFilter: "篩選", resetFilter: "重置", clearFilter: "全部", sumText: "合計" } } };
var me = Object.defineProperty;
var qe = Object.getOwnPropertyDescriptor;
var Ve = Object.getOwnPropertyNames;
var Ge = Object.prototype.hasOwnProperty;
var le = (e, t, r, c) => {
  if (t && typeof t == "object" || typeof t == "function")
    for (let u of Ve(t))
      !Ge.call(e, u) && u !== r && me(e, u, { get: () => t[u], enumerable: !(c = qe(t, u)) || c.enumerable });
  return e;
};
var Je = Object.prototype.toString;
function ye(e, t) {
  return Je.call(e) === `[object ${t}]`;
}
function ie(e) {
  return ye(e, "String");
}
function se(e) {
  return typeof e == "function";
}
var pe;
var ce;
var ue = (e) => e.replace(/\B([A-Z])/g, "-$1").toLowerCase();
var C = {};
((e, t) => {
  for (var r in t)
    me(e, r, { get: t[r], enumerable: true });
})(C, { Vue: () => vue_runtime_esm_bundler_exports }), le(C, pe = vue_runtime_esm_bundler_exports, "default"), ce && le(ce, pe, "default");
var Ue = (e) => {
  let t, r = (e == null ? void 0 : e.className) ?? "dark", c = (0, C.shallowRef)(false), u = () => {
    let d = e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement;
    c.value = d.classList.contains(r);
  };
  return function(d) {
    (0, C.getCurrentInstance)() && (0, C.onUnmounted)(d);
  }(() => {
    t && (t.takeRecords(), t.disconnect());
  }), (0, C.onBeforeMount)(() => {
    let d = e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement;
    u(), t = new MutationObserver(u), t.observe(d, { attributes: true, attributeFilter: ["class"] });
  }), { isDark: c, toggleDark: () => {
    (e != null && e.selector ? e.selector === "html" ? document.documentElement : document.body : document.documentElement).classList.toggle(r);
  } };
};
function de(e) {
  return typeof e == "function" || Object.prototype.toString.call(e) === "[object Object]" && !isVNode(e);
}
var H = defineComponent({ name: "PureTable", props: Ke, directives: { Loading: vLoading }, emits: ["page-size-change", "page-current-change"], setup(e, { slots: t, attrs: r, emit: c, expose: u }) {
  const { locale: d, i18n: i, ssr: j } = inject("locale", { locale: null, i18n: null, ssr: false }), { locale: x, columns: F, loading: w, tableKey: f, adaptive: b, pagination: p, alignWhole: $, headerAlign: be, loadingConfig: P, adaptiveConfig: T, rowHoverBgColor: M, showOverflowTooltip: he } = toRefs(e), I = ref(false), { isDark: ve } = Ue(), k = getCurrentInstance();
  let xe = unref(p) && unref(p).currentPage && unref(p).pageSize, W = computed(() => {
    var o, m, s, h;
    if (!unref(i))
      return;
    const n = ((s = (m = i == null ? void 0 : i.global) == null ? void 0 : m.getLocaleMessage(unref((o = i == null ? void 0 : i.global) == null ? void 0 : o.locale))) == null ? void 0 : s.el) || ((h = i == null ? void 0 : i.getLocaleMessage(unref(i == null ? void 0 : i.locale))) == null ? void 0 : h.el);
    return n ? { el: n } : null;
  }), K = computed(() => ie(d) ? [ne, oe, re].filter((n) => n.name === ue(d))[0] : d), A = computed(() => {
    if (unref(x))
      return ie(unref(x)) ? [ne, oe, re].filter((n) => n.name === ue(unref(x)))[0] : unref(x);
  }), we = computed(() => {
    if (!unref(P))
      return;
    let { text: n, spinner: o, svg: m, viewBox: s } = unref(P);
    return { "element-loading-text": n, "element-loading-spinner": o, "element-loading-svg": m, "element-loading-svg-view-box": s };
  });
  const Se = computed(() => {
    var n, o;
    if (unref(w))
      return { "element-loading-background": (n = unref(P)) != null && n.background ? (o = unref(P)) == null ? void 0 : o.background : ve.value ? "rgba(0, 0, 0, 0.45)" : "rgba(255, 255, 255, 0.45)" };
  }), Oe = computed(() => Object.assign({ width: "100%", margin: "16px 0", display: "flex", flexWrap: "wrap", justifyContent: unref(p).align === "left" ? "flex-start" : unref(p).align === "center" ? "center" : "flex-end" }, unref(p).style ?? {})), q = (n, o) => {
    const { cellRenderer: m, slot: s, headerRenderer: h, headerSlot: B, filterIconSlot: Q, expandSlot: X, hide: S, children: N, prop: E, ...Ce } = n;
    if (se(S) && S(r))
      return S(r);
    if (function(l) {
      return ye(l, "Boolean");
    }(S) && S)
      return S;
    const v = { default: (l) => {
      var g;
      return m ? createVNode(ae, { render: m, params: Object.assign(l, { index: l.$index, props: e, attrs: r }) }, null) : s ? (g = t == null ? void 0 : t[s]) == null ? void 0 : g.call(t, Object.assign(l, { index: l.$index, props: e, attrs: r })) : void 0;
    } };
    return h ? v.header = (l) => createVNode(ae, { render: h, params: Object.assign(l, { index: l.$index, props: e, attrs: r }) }, null) : t != null && t[B] && (v.header = (l) => {
      var g;
      return (g = t[B]) == null ? void 0 : g.call(t, Object.assign(l, { index: l.$index, props: e, attrs: r }));
    }), t != null && t[Q] && (v["filter-icon"] = (l) => {
      var g;
      return (g = t[Q]) == null ? void 0 : g.call(t, Object.assign(l, { index: l.$index, props: e, attrs: r }));
    }), t != null && t[X] && (v.expand = (l) => {
      var g;
      return (g = t[X]) == null ? void 0 : g.call(t, Object.assign(l, { index: l.$index, props: e, attrs: r }));
    }), (N == null ? void 0 : N.length) > 0 && (v.default = () => N.map(q)), createVNode(ElTableColumn, mergeProps({ key: o }, Ce, { prop: se(E) && E(o) ? E(o) : E, align: n != null && n.align ? n.align : unref($), headerAlign: n != null && n.headerAlign ? n.headerAlign : unref(be), showOverflowTooltip: n != null && n.showOverflowTooltip ? n.showOverflowTooltip : unref(he) }), de(v) ? v : { default: () => [v] });
  }, V = () => {
    var n;
    return (n = k == null ? void 0 : k.proxy) == null ? void 0 : n.$refs[`TableRef${unref(f)}`];
  }, z = () => V().$refs, D = async () => {
    await nextTick();
    const n = z().tableWrapper, o = unref(T).offsetBottom ?? 96;
    n.style.height = window.innerHeight - n.getBoundingClientRect().top - o + "px";
  }, G = /* @__PURE__ */ ((n, o = 200, m = false) => {
    let s, h, B = o;
    return function() {
      s && clearTimeout(s), m ? (s || n.call(h, ...arguments), s = setTimeout(() => s = null, B)) : s = setTimeout(() => n.call(h, ...arguments), B);
    };
  })(D, unref(T).timeout ?? 60), J = async (n = 3) => {
    await nextTick();
    const o = z().tableHeaderRef.$el.style;
    o.position = "sticky", o.top = 0, o.zIndex = n;
  };
  onMounted(() => {
    I.value = true, nextTick(() => {
      if (unref(M) && z().tableWrapper.style.setProperty("--el-table-row-hover-bg-color", unref(M), "important"), unref(b)) {
        if (D(), window.addEventListener("resize", G), Reflect.has(unref(T), "fixHeader") && !unref(T).fixHeader)
          return;
        J(unref(T).zIndex ?? 3);
      }
    });
  }), onBeforeUnmount(() => {
    unref(b) && window.removeEventListener("resize", G);
  }), u({ getTableRef: V, getTableDoms: z, setAdaptive: D, setHeaderSticky: J });
  let U = () => createVNode(Fragment, null, [createVNode(ElTable, mergeProps(e, r, { ref: `TableRef${unref(f)}` }), { default: () => unref(F).map(q), append: () => t.append && t.append(), empty: () => t.empty && t.empty() }), xe ? createVNode(ElPagination, mergeProps(r, { class: "pure-pagination", style: unref(Oe) }, unref(p), { layout: unref(p).layout ?? "total, sizes, prev, pager, next, jumper", pageSizes: unref(p).pageSizes ?? [5, 10, 15, 20], onSizeChange: (n) => ((o) => {
    unref(p).pageSize = o, c("page-size-change", o);
  })(n), onCurrentChange: (n) => ((o) => {
    unref(p).currentPage = o, c("page-current-change", o);
  })(n) }), null) : null]), _ = () => {
    let n;
    return withDirectives(createVNode("div", mergeProps({ class: "pure-table", style: "width:100%" }, unref(Se), unref(we)), [unref(W) || unref(K) || unref(A) ? createVNode(ElConfigProvider, { locale: unref(A) ? unref(A) : unref(W) || unref(K) }, de(n = U()) ? n : { default: () => [n] }) : U()]), [[resolveDirective("loading"), unref(w)]]);
  };
  return () => j ? I.value && _() : _();
} });
var Xe = Object.assign(H, { install: (e, t) => {
  e.component(H.name, H), e.provide("locale", t ?? { locale: null, i18n: null, ssr: false });
} });
export {
  Xe as PureTable,
  Xe as default
};
/*! Bundled license information:

@pureadmin/table/dist/index.es.js:
  (**
  * @vue/shared v3.4.37
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **)
*/
//# sourceMappingURL=@pureadmin_table.js.map
