# Generated by Django 4.2.14 on 2025-08-11 08:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SysUser',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('username', models.CharField(max_length=100, unique=True, verbose_name='用户名')),
                ('password', models.CharField(max_length=100, verbose_name='密码')),
                ('nickname', models.Char<PERSON>ield(max_length=100, null=True, verbose_name='昵称')),
                ('avatar', models.CharField(max_length=255, null=True, verbose_name='用户头像')),
                ('email', models.CharField(max_length=100, null=True, verbose_name='用户邮箱')),
                ('phonenumber', models.CharField(max_length=11, null=True, verbose_name='手机号码')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('login_date', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('status', models.IntegerField(default=0, verbose_name='帐号状态（0正常 1停用）')),
                ('remark', models.CharField(blank=True, max_length=500, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '系统用户',
                'verbose_name_plural': '系统用户',
                'db_table': 'sys_user',
                'ordering': ['-create_time'],
            },
        ),
    ]
