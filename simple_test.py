#!/usr/bin/env python3
"""
简化的API测试脚本
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_login():
    """测试登录"""
    print("🔐 测试登录...")
    
    data = {"username": "admin", "password": "admin@123"}
    
    try:
        response = requests.post(f"{BASE_URL}/user/login", json=data)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            return result['data']['token']
        else:
            print("❌ 登录失败")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_user_info(token):
    """测试获取用户信息"""
    print("\n👤 测试获取用户信息...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f"{BASE_URL}/user/info", headers=headers)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    print("🚀 开始API测试...")
    print("="*50)
    
    # 测试登录
    token = test_login()
    
    if token:
        # 测试获取用户信息
        test_user_info(token)
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
