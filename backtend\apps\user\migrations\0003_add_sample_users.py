# Generated manually to add sample users

from django.db import migrations
from django.utils import timezone


def create_sample_users(apps, schema_editor):
    SysUser = apps.get_model('user', 'SysUser')
    
    # Create admin user
    admin_user, created = SysUser.objects.get_or_create(
        username='admin',
        defaults={
            'password': 'admin@123',
            'avatar': 'https://avatars.githubusercontent.com/u/167622633',
            'email': '<EMAIL>',
            'status': 0,  # 0 = active
            'create_time': timezone.now(),
            'remark': '系统管理员'
        }
    )
    
    # Create common user
    common_user, created = SysUser.objects.get_or_create(
        username='common',
        defaults={
            'password': 'common@123',
            'avatar': 'https://avatars.githubusercontent.com/u/52823142',
            'email': '<EMAIL>',
            'status': 0,  # 0 = active
            'create_time': timezone.now(),
            'remark': '普通用户'
        }
    )


def remove_sample_users(apps, schema_editor):
    SysUser = apps.get_model('user', 'SysUser')
    SysUser.objects.filter(username__in=['admin', 'common']).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0002_alter_sysuser_table'),
    ]

    operations = [
        migrations.RunPython(create_sample_users, remove_sample_users),
    ]
