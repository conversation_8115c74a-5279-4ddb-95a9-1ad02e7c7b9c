#!/usr/bin/env python3
"""
测试JWT token生成和解析的脚本
"""

import requests
import json

def test_jwt_flow():
    login_url = "http://localhost:8000/user/login"
    info_url = "http://localhost:8000/user/info"
    
    print("🔐 测试JWT token流程...")
    print("="*50)
    
    # 1. 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin@123"
    }
    
    print("1️⃣ 登录获取token...")
    try:
        response = requests.post(login_url, json=login_data, headers={'Content-Type': 'application/json'})
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            login_result = response.json()
            print(f"登录响应: {json.dumps(login_result, indent=2, ensure_ascii=False)}")
            
            if login_result.get('success') and 'token' in login_result.get('data', {}):
                token = login_result['data']['token']
                print(f"\n✅ 获取到token: {token[:50]}...")
                
                # 2. 使用token获取用户信息
                print(f"\n2️⃣ 使用token获取用户信息...")
                info_response = requests.get(info_url, headers={
                    'Authorization': f'Bearer {token}',
                    'Content-Type': 'application/json'
                })
                print(f"状态码: {info_response.status_code}")
                
                if info_response.status_code == 200:
                    user_info = info_response.json()
                    print(f"✅ 用户信息: {json.dumps(user_info, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ 获取用户信息失败: {info_response.text}")
            else:
                print("❌ 登录失败，未获取到token")
        else:
            print(f"❌ 登录请求失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器，请确保Django服务器在8000端口运行")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_jwt_flow()
