// 连接后端API的登录接口
import { defineFakeRoute } from "vite-plugin-fake-server/client";

export default defineFakeRoute([
  {
    url: "/login",
    method: "post",
    response: async ({ body }) => {
      try {
        // 通过代理调用后端登录API (使用 /api 前缀会被代理到 http://localhost:8000)
        const response = await fetch('/api/user/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: body.username,
            password: body.password
          })
        });

        const result = await response.json();

        // 如果后端返回成功，直接返回后端的响应
        if (result.success) {
          return result;
        } else {
          // 如果后端返回失败，返回错误信息
          return {
            success: false,
            message: result.message || '登录失败'
          };
        }
      } catch (error) {
        // 如果后端服务不可用，返回服务不可用错误
        console.error('后端服务不可用:', error);

        return {
          success: false,
          message: '服务器连接失败，请检查后端服务是否正常运行'
        };
      }
    }
  }
]);
